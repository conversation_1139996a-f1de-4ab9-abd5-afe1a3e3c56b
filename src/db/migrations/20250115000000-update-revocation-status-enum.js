'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      console.log('Starting migration to update revocation status enum...');
      
      // Check if the enum type exists
      const enumExists = await queryInterface.sequelize.query(`
        SELECT EXISTS (
          SELECT 1 FROM pg_type WHERE typname = 'enum_Revocations_status'
        );
      `);

      if (enumExists[0][0].exists) {
        console.log('Enum type exists. Updating it...');
        
        // First, add the new enum values if they don't exist
        try {
          await queryInterface.sequelize.query(`
            ALTER TYPE "enum_Revocations_status" ADD VALUE IF NOT EXISTS 'Approved by HR';
          `);
          console.log('Added "Approved by HR" to enum');
        } catch (error) {
          console.log('Value "Approved by HR" may already exist:', error.message);
        }

        try {
          await queryInterface.sequelize.query(`
            ALTER TYPE "enum_Revocations_status" ADD VALUE IF NOT EXISTS 'Approved by IT';
          `);
          console.log('Added "Approved by IT" to enum');
        } catch (error) {
          console.log('Value "Approved by IT" may already exist:', error.message);
        }

        // Remove the old 'Completed' value if it exists and no records use it
        try {
          const completedCount = await queryInterface.sequelize.query(`
            SELECT COUNT(*) FROM "Revocations" WHERE status = 'Completed';
          `);
          
          if (completedCount[0][0].count === '0') {
            console.log('No records use "Completed" status. It can be safely removed in future migrations.');
          } else {
            console.log(`Found ${completedCount[0][0].count} records with "Completed" status. Keeping the value.`);
          }
        } catch (error) {
          console.log('Error checking for Completed status usage:', error.message);
        }

      } else {
        console.log('Enum type does not exist. Creating new column with updated enum...');
        
        // If the enum doesn't exist, recreate the column with the new enum
        await queryInterface.changeColumn('Revocations', 'status', {
          type: Sequelize.ENUM('Pending', 'Approved by HR', 'Approved by IT', 'Rejected'),
          allowNull: false,
          defaultValue: 'Pending'
        });
      }

      console.log('Migration completed successfully!');
      
    } catch (error) {
      console.error('Error during migration:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      console.log('Rolling back revocation status enum changes...');
      
      // Revert to the original enum values
      await queryInterface.changeColumn('Revocations', 'status', {
        type: Sequelize.ENUM('Pending', 'Completed', 'Rejected'),
        allowNull: false,
        defaultValue: 'Pending'
      });
      
      console.log('Rollback completed successfully!');
      
    } catch (error) {
      console.error('Error during rollback:', error);
      throw error;
    }
  }
};
