'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      console.log('Adding subsidiary column to Revocations table...');
      
      // Check if the column already exists
      const tableDescription = await queryInterface.describeTable('Revocations');
      
      if (!tableDescription.subsidiary) {
        await queryInterface.addColumn('Revocations', 'subsidiary', {
          type: Sequelize.STRING,
          allowNull: false,
          defaultValue: 'platinumkenya'
        });
        console.log('Successfully added subsidiary column to Revocations table');
      } else {
        console.log('Subsidiary column already exists in Revocations table');
      }
      
    } catch (error) {
      console.error('Error adding subsidiary column:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      console.log('Removing subsidiary column from Revocations table...');
      
      await queryInterface.removeColumn('Revocations', 'subsidiary');
      console.log('Successfully removed subsidiary column from Revocations table');
      
    } catch (error) {
      console.error('Error removing subsidiary column:', error);
      throw error;
    }
  }
};
